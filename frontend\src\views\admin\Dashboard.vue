<template>
  <div class="dashboard">
    <n-layout has-sider>
      <n-layout-sider bordered collapse-mode="width" :collapsed-width="64" :width="240">
        <n-menu 
          :collapsed="collapsed"
          :options="menuOptions"
          :value="activeKey"
          @update:value="handleMenuSelect"
        />
      </n-layout-sider>
      
      <n-layout>
        <n-layout-header bordered style="height: 64px; padding: 0 24px;">
          <div style="display: flex; align-items: center; justify-content: space-between; height: 100%;">
            <n-button quaternary @click="collapsed = !collapsed">
              <template #icon>
                <n-icon :component="MenuOutline" />
              </template>
            </n-button>
            
            <n-space>
              <n-text>欢迎，管理员</n-text>
              <n-button @click="handleLogout">退出登录</n-button>
            </n-space>
          </div>
        </n-layout-header>
        
        <n-layout-content style="padding: 24px;">
          <n-h2>项目管理</n-h2>
          <n-text depth="3">这里将显示项目列表和管理功能</n-text>
          
          <n-divider />
          
          <n-button type="primary" @click="message.info('创建项目功能即将开发')">
            创建新项目
          </n-button>
        </n-layout-content>
      </n-layout>
    </n-layout>
  </div>
</template>

<script setup>
import { ref, h } from 'vue'
import { useRouter } from 'vue-router'
import { useMessage } from 'naive-ui'
import { MenuOutline } from '@vicons/ionicons5'
import {
  NLayout, NLayoutSider, NLayoutHeader, NLayoutContent,
  NMenu, NButton, NIcon, NSpace, NText, NH2, NDivider
} from 'naive-ui'

const router = useRouter()
const message = useMessage()

const collapsed = ref(false)
const activeKey = ref('projects')

const menuOptions = [
  {
    label: '项目管理',
    key: 'projects',
    icon: () => h('span', '📋')
  },
  {
    label: '数据统计',
    key: 'statistics',
    icon: () => h('span', '📊')
  }
]

const handleMenuSelect = (key) => {
  activeKey.value = key
  message.info(`切换到: ${key}`)
}

const handleLogout = () => {
  localStorage.removeItem('admin_token')
  message.success('已退出登录')
  router.push('/admin')
}
</script>

<style scoped>
.dashboard {
  height: 100vh;
}
</style>
