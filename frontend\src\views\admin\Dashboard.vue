<template>
  <div class="dashboard">
    <n-layout has-sider>
      <n-layout-sider bordered collapse-mode="width" :collapsed-width="64" :width="240">
        <n-menu 
          :collapsed="collapsed"
          :options="menuOptions"
          :value="activeKey"
          @update:value="handleMenuSelect"
        />
      </n-layout-sider>
      
      <n-layout>
        <n-layout-header bordered style="height: 64px; padding: 0 24px;">
          <div style="display: flex; align-items: center; justify-content: space-between; height: 100%;">
            <n-button quaternary @click="collapsed = !collapsed">
              ☰
            </n-button>
            
            <n-space>
              <n-text>欢迎，管理员</n-text>
              <n-button @click="handleLogout">退出登录</n-button>
            </n-space>
          </div>
        </n-layout-header>
        
        <n-layout-content style="padding: 24px;">
          <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 24px;">
            <n-h2>项目管理</n-h2>
            <n-button type="primary" @click="handleCreateProject">
              创建新项目
            </n-button>
          </div>

          <n-card>
            <n-data-table
              :columns="columns"
              :data="projects"
              :loading="loading"
              :pagination="pagination"
            />
          </n-card>
        </n-layout-content>
      </n-layout>
    </n-layout>
  </div>
</template>

<script setup>
import { ref, h, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useMessage } from 'naive-ui'
// import { MenuOutline } from '@vicons/ionicons5'
import { projectsApi } from '@/api/projects'
import {
  NLayout, NLayoutSider, NLayoutHeader, NLayoutContent,
  NMenu, NButton, NIcon, NSpace, NText, NH2, NDivider,
  NCard, NDataTable
} from 'naive-ui'

const router = useRouter()
const message = useMessage()

const collapsed = ref(false)
const activeKey = ref('projects')
const loading = ref(false)
const projects = ref([])

// 表格配置
const columns = [
  {
    title: 'ID',
    key: 'id',
    width: 80
  },
  {
    title: '项目标题',
    key: 'title'
  },
  {
    title: '项目描述',
    key: 'description'
  },
  {
    title: '分享链接',
    key: 'share_link',
    render: (row) => h('code', { style: 'font-size: 12px;' }, row.share_link)
  },
  {
    title: '创建时间',
    key: 'created_at',
    render: (row) => new Date(row.created_at).toLocaleString()
  },
  {
    title: '操作',
    key: 'actions',
    render: (row) => h('div', [
      h(NButton, {
        size: 'small',
        style: 'margin-right: 8px;',
        onClick: () => handleViewProject(row.id)
      }, '查看'),
      h(NButton, {
        size: 'small',
        type: 'primary',
        style: 'margin-right: 8px;',
        onClick: () => handleEditProject(row.id)
      }, '编辑'),
      h(NButton, {
        size: 'small',
        type: 'error',
        onClick: () => handleDeleteProject(row.id)
      }, '删除')
    ])
  }
]

const pagination = {
  pageSize: 10
}

const menuOptions = [
  {
    label: '项目管理',
    key: 'projects',
    icon: () => h('span', '📋')
  },
  {
    label: '数据统计',
    key: 'statistics',
    icon: () => h('span', '📊')
  }
]

// 获取项目列表
const fetchProjects = async () => {
  try {
    loading.value = true
    const response = await projectsApi.getProjects()
    projects.value = response
  } catch (error) {
    console.error('获取项目列表失败:', error)
    message.error('获取项目列表失败')
  } finally {
    loading.value = false
  }
}

// 页面加载时获取数据
onMounted(() => {
  fetchProjects()
})

const handleMenuSelect = (key) => {
  activeKey.value = key
  message.info(`切换到: ${key}`)
}

const handleCreateProject = () => {
  message.info('创建项目功能即将开发')
}

const handleViewProject = (projectId) => {
  message.info(`查看项目 ${projectId}`)
}

const handleEditProject = (projectId) => {
  message.info(`编辑项目 ${projectId}`)
}

const handleDeleteProject = (projectId) => {
  message.info(`删除项目 ${projectId}`)
}

const handleLogout = () => {
  localStorage.removeItem('admin_token')
  message.success('已退出登录')
  router.push('/admin')
}
</script>

<style scoped>
.dashboard {
  height: 100vh;
}
</style>
