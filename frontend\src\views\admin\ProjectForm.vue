<template>
  <div class="project-form">
    <n-card>
      <template #header>
        <div style="display: flex; align-items: center; justify-content: space-between;">
          <n-text strong>{{ isEdit ? '编辑项目' : '创建项目' }}</n-text>
          <n-button quaternary @click="$router.back()">
            返回
          </n-button>
        </div>
      </template>

      <n-form ref="formRef" :model="formData" :rules="rules" label-placement="top">
        <n-grid :cols="1" :x-gap="16" :y-gap="16">
          <n-grid-item>
            <n-form-item label="项目标题" path="title">
              <n-input 
                v-model:value="formData.title" 
                placeholder="请输入项目标题"
                size="large"
              />
            </n-form-item>
          </n-grid-item>

          <n-grid-item>
            <n-form-item label="项目描述" path="description">
              <n-input 
                v-model:value="formData.description" 
                type="textarea"
                placeholder="请输入项目描述"
                :rows="3"
                size="large"
              />
            </n-form-item>
          </n-grid-item>

          <n-grid-item>
            <n-form-item label="表单字段配置">
              <n-card>
                <template #header>
                  <div style="display: flex; justify-content: space-between; align-items: center;">
                    <n-text>表单字段</n-text>
                    <n-button type="primary" size="small" @click="addField">
                      添加字段
                    </n-button>
                  </div>
                </template>

                <n-space vertical>
                  <div v-for="(field, index) in formData.form_config.fields" :key="index" class="field-item">
                    <n-card size="small">
                      <n-grid :cols="4" :x-gap="12">
                        <n-grid-item>
                          <n-form-item label="字段名称">
                            <n-input v-model:value="field.name" placeholder="字段名称" />
                          </n-form-item>
                        </n-grid-item>
                        <n-grid-item>
                          <n-form-item label="显示标签">
                            <n-input v-model:value="field.label" placeholder="显示标签" />
                          </n-form-item>
                        </n-grid-item>
                        <n-grid-item>
                          <n-form-item label="字段类型">
                            <n-select v-model:value="field.type" :options="fieldTypeOptions" />
                          </n-form-item>
                        </n-grid-item>
                        <n-grid-item>
                          <n-form-item label="操作">
                            <n-space>
                              <n-checkbox v-model:checked="field.required">必填</n-checkbox>
                              <n-button type="error" size="small" @click="removeField(index)">
                                删除
                              </n-button>
                            </n-space>
                          </n-form-item>
                        </n-grid-item>
                      </n-grid>
                    </n-card>
                  </div>

                  <n-empty v-if="formData.form_config.fields.length === 0" description="暂无字段，请添加表单字段" />
                </n-space>
              </n-card>
            </n-form-item>
          </n-grid-item>

          <n-grid-item>
            <n-space>
              <n-button 
                type="primary" 
                size="large"
                :loading="loading"
                @click="handleSubmit"
              >
                {{ isEdit ? '更新项目' : '创建项目' }}
              </n-button>
              <n-button size="large" @click="handleReset">
                重置
              </n-button>
            </n-space>
          </n-grid-item>
        </n-grid>
      </n-form>
    </n-card>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useMessage } from 'naive-ui'
import { projectsApi } from '@/api/projects'
import {
  NCard, NText, NButton, NForm, NFormItem, NInput, NGrid, NGridItem,
  NSpace, NSelect, NCheckbox, NEmpty
} from 'naive-ui'

const route = useRoute()
const router = useRouter()
const message = useMessage()

const formRef = ref(null)
const loading = ref(false)

// 判断是否为编辑模式
const isEdit = computed(() => !!route.params.id)

// 表单数据
const formData = ref({
  title: '',
  description: '',
  form_config: {
    fields: []
  }
})

// 字段类型选项
const fieldTypeOptions = [
  { label: '文本输入', value: 'text' },
  { label: '邮箱', value: 'email' },
  { label: '电话', value: 'tel' },
  { label: '数字', value: 'number' },
  { label: '多行文本', value: 'textarea' },
  { label: '选择框', value: 'select' },
  { label: '日期', value: 'date' }
]

// 表单验证规则
const rules = {
  title: {
    required: true,
    message: '请输入项目标题',
    trigger: 'blur'
  },
  description: {
    required: true,
    message: '请输入项目描述',
    trigger: 'blur'
  }
}

// 添加字段
const addField = () => {
  formData.value.form_config.fields.push({
    name: '',
    label: '',
    type: 'text',
    required: false
  })
}

// 删除字段
const removeField = (index) => {
  formData.value.form_config.fields.splice(index, 1)
}

// 重置表单
const handleReset = () => {
  formData.value = {
    title: '',
    description: '',
    form_config: {
      fields: []
    }
  }
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    
    // 验证表单字段配置
    if (formData.value.form_config.fields.length === 0) {
      message.warning('请至少添加一个表单字段')
      return
    }

    // 验证字段配置完整性
    for (const field of formData.value.form_config.fields) {
      if (!field.name || !field.label) {
        message.warning('请完善所有字段的名称和标签')
        return
      }
    }

    loading.value = true

    if (isEdit.value) {
      // 更新项目
      await projectsApi.updateProject(route.params.id, formData.value)
      message.success('项目更新成功')
    } else {
      // 创建项目
      await projectsApi.createProject(formData.value)
      message.success('项目创建成功')
    }

    router.push('/admin/dashboard')

  } catch (error) {
    console.error('提交失败:', error)
    message.error('操作失败，请重试')
  } finally {
    loading.value = false
  }
}

// 加载项目数据（编辑模式）
const loadProject = async () => {
  if (!isEdit.value) return

  try {
    loading.value = true
    const project = await projectsApi.getProject(route.params.id)
    
    formData.value = {
      title: project.title,
      description: project.description,
      form_config: project.form_config || { fields: [] }
    }
  } catch (error) {
    console.error('加载项目失败:', error)
    message.error('加载项目失败')
    router.push('/admin/dashboard')
  } finally {
    loading.value = false
  }
}

// 页面加载时执行
onMounted(() => {
  if (isEdit.value) {
    loadProject()
  } else {
    // 新建项目时添加默认字段
    formData.value.form_config.fields = [
      { name: 'name', label: '姓名', type: 'text', required: true },
      { name: 'phone', label: '电话', type: 'tel', required: true },
      { name: 'email', label: '邮箱', type: 'email', required: false }
    ]
  }
})
</script>

<style scoped>
.project-form {
  padding: 24px;
}

.field-item {
  margin-bottom: 16px;
}
</style>
