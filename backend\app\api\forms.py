import json
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from app.db.database import get_db
from app.models.project import Project, Registration
from app.schemas.project import ProjectResponse, RegistrationCreate, RegistrationResponse

router = APIRouter()

@router.get("/{share_link}", response_model=ProjectResponse)
async def get_form_config(share_link: str, db: Session = Depends(get_db)):
    """根据分享链接获取表单配置"""
    project = db.query(Project).filter(Project.share_link == share_link).first()
    if not project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="表单不存在或链接已失效"
        )
    return project

@router.post("/{share_link}/submit", response_model=RegistrationResponse)
async def submit_form(
    share_link: str,
    registration: RegistrationCreate,
    db: Session = Depends(get_db)
):
    """提交表单数据"""
    # 验证项目是否存在
    project = db.query(Project).filter(Project.share_link == share_link).first()
    if not project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="表单不存在或链接已失效"
        )
    
    # 创建登记记录
    db_registration = Registration(
        project_id=project.id,
        form_data=json.dumps(registration.form_data)
    )
    db.add(db_registration)
    db.commit()
    db.refresh(db_registration)
    
    return db_registration
