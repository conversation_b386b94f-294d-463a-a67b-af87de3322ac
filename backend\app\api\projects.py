import uuid
import json
from typing import List
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from app.db.database import get_db
from app.models.project import Project, Registration
from app.schemas.project import (
    ProjectCreate, ProjectUpdate, ProjectResponse,
    ProjectWithRegistrations, RegistrationResponse
)

router = APIRouter()

@router.post("/", response_model=ProjectResponse)
async def create_project(
    project: ProjectCreate,
    db: Session = Depends(get_db)
):
    """创建新项目"""
    # 生成唯一的分享链接
    share_link = str(uuid.uuid4())
    
    db_project = Project(
        title=project.title,
        description=project.description,
        form_config=json.dumps(project.form_config) if project.form_config else None,
        share_link=share_link
    )
    db.add(db_project)
    db.commit()
    db.refresh(db_project)
    
    return db_project

@router.get("/", response_model=List[ProjectResponse])
async def get_projects(db: Session = Depends(get_db)):
    """获取所有项目"""
    projects = db.query(Project).all()
    return projects

@router.get("/{project_id}", response_model=ProjectWithRegistrations)
async def get_project(project_id: int, db: Session = Depends(get_db)):
    """获取项目详情"""
    project = db.query(Project).filter(Project.id == project_id).first()
    if not project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="项目不存在"
        )
    return project

@router.put("/{project_id}", response_model=ProjectResponse)
async def update_project(
    project_id: int,
    project_update: ProjectUpdate,
    db: Session = Depends(get_db)
):
    """更新项目"""
    project = db.query(Project).filter(Project.id == project_id).first()
    if not project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="项目不存在"
        )

    # 更新字段
    for field, value in project_update.dict(exclude_unset=True).items():
        if field == 'form_config' and value is not None:
            setattr(project, field, json.dumps(value))
        else:
            setattr(project, field, value)

    db.commit()
    db.refresh(project)
    return project

@router.delete("/{project_id}")
async def delete_project(project_id: int, db: Session = Depends(get_db)):
    """删除项目"""
    project = db.query(Project).filter(Project.id == project_id).first()
    if not project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="项目不存在"
        )

    db.delete(project)
    db.commit()
    return {"message": "项目已删除"}

@router.get("/{project_id}/registrations", response_model=List[RegistrationResponse])
async def get_project_registrations(project_id: int, db: Session = Depends(get_db)):
    """获取项目的所有登记信息"""
    project = db.query(Project).filter(Project.id == project_id).first()
    if not project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="项目不存在"
        )

    registrations = db.query(Registration).filter(Registration.project_id == project_id).all()
    return registrations
