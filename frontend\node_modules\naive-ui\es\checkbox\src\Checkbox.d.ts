import type { CheckboxInst, OnUpdateChecked } from './interface';
import { type PropType } from 'vue';
import { type ExtractPublicPropTypes, type MaybeArray } from '../../_utils';
export declare const checkboxProps: {
    size: PropType<"small" | "medium" | "large">;
    checked: {
        type: PropType<boolean | string | number | undefined>;
        default: undefined;
    };
    defaultChecked: {
        type: PropType<boolean | string | number>;
        default: boolean;
    };
    value: PropType<string | number>;
    disabled: {
        type: PropType<boolean | undefined>;
        default: undefined;
    };
    indeterminate: BooleanConstructor;
    label: StringConstructor;
    focusable: {
        type: BooleanConstructor;
        default: boolean;
    };
    checkedValue: {
        type: (BooleanConstructor | StringConstructor | NumberConstructor)[];
        default: boolean;
    };
    uncheckedValue: {
        type: (BooleanConstructor | StringConstructor | NumberConstructor)[];
        default: boolean;
    };
    'onUpdate:checked': PropType<MaybeArray<OnUpdateChecked>>;
    onUpdateChecked: PropType<MaybeArray<OnUpdateChecked>>;
    privateInsideTable: BooleanConstructor;
    onChange: PropType<MaybeArray<OnUpdateChecked>>;
    theme: PropType<import("../../_mixins").Theme<"Checkbox", {
        labelLineHeight: string;
        fontSizeSmall: string;
        fontSizeMedium: string;
        fontSizeLarge: string;
        borderRadius: string;
        color: string;
        colorChecked: string;
        colorDisabled: string;
        colorDisabledChecked: string;
        colorTableHeader: string;
        colorTableHeaderModal: string;
        colorTableHeaderPopover: string;
        checkMarkColor: string;
        checkMarkColorDisabled: string;
        checkMarkColorDisabledChecked: string;
        border: string;
        borderDisabled: string;
        borderDisabledChecked: string;
        borderChecked: string;
        borderFocus: string;
        boxShadowFocus: string;
        textColor: string;
        textColorDisabled: string;
        sizeSmall: string;
        sizeMedium: string;
        sizeLarge: string;
        labelPadding: string;
        labelFontWeight: string;
    }, any>>;
    themeOverrides: PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Checkbox", {
        labelLineHeight: string;
        fontSizeSmall: string;
        fontSizeMedium: string;
        fontSizeLarge: string;
        borderRadius: string;
        color: string;
        colorChecked: string;
        colorDisabled: string;
        colorDisabledChecked: string;
        colorTableHeader: string;
        colorTableHeaderModal: string;
        colorTableHeaderPopover: string;
        checkMarkColor: string;
        checkMarkColorDisabled: string;
        checkMarkColorDisabledChecked: string;
        border: string;
        borderDisabled: string;
        borderDisabledChecked: string;
        borderChecked: string;
        borderFocus: string;
        boxShadowFocus: string;
        textColor: string;
        textColorDisabled: string;
        sizeSmall: string;
        sizeMedium: string;
        sizeLarge: string;
        labelPadding: string;
        labelFontWeight: string;
    }, any>>>;
    builtinThemeOverrides: PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Checkbox", {
        labelLineHeight: string;
        fontSizeSmall: string;
        fontSizeMedium: string;
        fontSizeLarge: string;
        borderRadius: string;
        color: string;
        colorChecked: string;
        colorDisabled: string;
        colorDisabledChecked: string;
        colorTableHeader: string;
        colorTableHeaderModal: string;
        colorTableHeaderPopover: string;
        checkMarkColor: string;
        checkMarkColorDisabled: string;
        checkMarkColorDisabledChecked: string;
        border: string;
        borderDisabled: string;
        borderDisabledChecked: string;
        borderChecked: string;
        borderFocus: string;
        boxShadowFocus: string;
        textColor: string;
        textColorDisabled: string;
        sizeSmall: string;
        sizeMedium: string;
        sizeLarge: string;
        labelPadding: string;
        labelFontWeight: string;
    }, any>>>;
};
export type CheckboxProps = ExtractPublicPropTypes<typeof checkboxProps>;
declare const _default: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    size: PropType<"small" | "medium" | "large">;
    checked: {
        type: PropType<boolean | string | number | undefined>;
        default: undefined;
    };
    defaultChecked: {
        type: PropType<boolean | string | number>;
        default: boolean;
    };
    value: PropType<string | number>;
    disabled: {
        type: PropType<boolean | undefined>;
        default: undefined;
    };
    indeterminate: BooleanConstructor;
    label: StringConstructor;
    focusable: {
        type: BooleanConstructor;
        default: boolean;
    };
    checkedValue: {
        type: (BooleanConstructor | StringConstructor | NumberConstructor)[];
        default: boolean;
    };
    uncheckedValue: {
        type: (BooleanConstructor | StringConstructor | NumberConstructor)[];
        default: boolean;
    };
    'onUpdate:checked': PropType<MaybeArray<OnUpdateChecked>>;
    onUpdateChecked: PropType<MaybeArray<OnUpdateChecked>>;
    privateInsideTable: BooleanConstructor;
    onChange: PropType<MaybeArray<OnUpdateChecked>>;
    theme: PropType<import("../../_mixins").Theme<"Checkbox", {
        labelLineHeight: string;
        fontSizeSmall: string;
        fontSizeMedium: string;
        fontSizeLarge: string;
        borderRadius: string;
        color: string;
        colorChecked: string;
        colorDisabled: string;
        colorDisabledChecked: string;
        colorTableHeader: string;
        colorTableHeaderModal: string;
        colorTableHeaderPopover: string;
        checkMarkColor: string;
        checkMarkColorDisabled: string;
        checkMarkColorDisabledChecked: string;
        border: string;
        borderDisabled: string;
        borderDisabledChecked: string;
        borderChecked: string;
        borderFocus: string;
        boxShadowFocus: string;
        textColor: string;
        textColorDisabled: string;
        sizeSmall: string;
        sizeMedium: string;
        sizeLarge: string;
        labelPadding: string;
        labelFontWeight: string;
    }, any>>;
    themeOverrides: PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Checkbox", {
        labelLineHeight: string;
        fontSizeSmall: string;
        fontSizeMedium: string;
        fontSizeLarge: string;
        borderRadius: string;
        color: string;
        colorChecked: string;
        colorDisabled: string;
        colorDisabledChecked: string;
        colorTableHeader: string;
        colorTableHeaderModal: string;
        colorTableHeaderPopover: string;
        checkMarkColor: string;
        checkMarkColorDisabled: string;
        checkMarkColorDisabledChecked: string;
        border: string;
        borderDisabled: string;
        borderDisabledChecked: string;
        borderChecked: string;
        borderFocus: string;
        boxShadowFocus: string;
        textColor: string;
        textColorDisabled: string;
        sizeSmall: string;
        sizeMedium: string;
        sizeLarge: string;
        labelPadding: string;
        labelFontWeight: string;
    }, any>>>;
    builtinThemeOverrides: PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Checkbox", {
        labelLineHeight: string;
        fontSizeSmall: string;
        fontSizeMedium: string;
        fontSizeLarge: string;
        borderRadius: string;
        color: string;
        colorChecked: string;
        colorDisabled: string;
        colorDisabledChecked: string;
        colorTableHeader: string;
        colorTableHeaderModal: string;
        colorTableHeaderPopover: string;
        checkMarkColor: string;
        checkMarkColorDisabled: string;
        checkMarkColorDisabledChecked: string;
        border: string;
        borderDisabled: string;
        borderDisabledChecked: string;
        borderChecked: string;
        borderFocus: string;
        boxShadowFocus: string;
        textColor: string;
        textColorDisabled: string;
        sizeSmall: string;
        sizeMedium: string;
        sizeLarge: string;
        labelPadding: string;
        labelFontWeight: string;
    }, any>>>;
}>, import("../../_mixins/use-form-item").UseFormItem<"small" | "medium" | "large"> & CheckboxInst & {
    rtlEnabled: import("vue").Ref<import("../../config-provider/src/internal-interface").RtlItem | undefined, import("../../config-provider/src/internal-interface").RtlItem | undefined> | undefined;
    selfRef: import("vue").Ref<HTMLDivElement | null, HTMLDivElement | null>;
    mergedClsPrefix: import("vue").Ref<string, string>;
    mergedDisabled: import("vue").ComputedRef<boolean>;
    renderedChecked: import("vue").ComputedRef<boolean>;
    mergedTheme: import("vue").ComputedRef<{
        common: import("../..").ThemeCommonVars;
        self: {
            labelLineHeight: string;
            fontSizeSmall: string;
            fontSizeMedium: string;
            fontSizeLarge: string;
            borderRadius: string;
            color: string;
            colorChecked: string;
            colorDisabled: string;
            colorDisabledChecked: string;
            colorTableHeader: string;
            colorTableHeaderModal: string;
            colorTableHeaderPopover: string;
            checkMarkColor: string;
            checkMarkColorDisabled: string;
            checkMarkColorDisabledChecked: string;
            border: string;
            borderDisabled: string;
            borderDisabledChecked: string;
            borderChecked: string;
            borderFocus: string;
            boxShadowFocus: string;
            textColor: string;
            textColorDisabled: string;
            sizeSmall: string;
            sizeMedium: string;
            sizeLarge: string;
            labelPadding: string;
            labelFontWeight: string;
        };
        peers: any;
        peerOverrides: {
            [x: string]: any;
        };
    }>;
    labelId: string;
    handleClick: (e: MouseEvent) => void;
    handleKeyUp: (e: KeyboardEvent) => void;
    handleKeyDown: (e: KeyboardEvent) => void;
    cssVars: import("vue").ComputedRef<{
        '--n-label-line-height': string;
        '--n-label-font-weight': string;
        '--n-size': string;
        '--n-bezier': string;
        '--n-border-radius': string;
        '--n-border': string;
        '--n-border-checked': string;
        '--n-border-focus': string;
        '--n-border-disabled': string;
        '--n-border-disabled-checked': string;
        '--n-box-shadow-focus': string;
        '--n-color': string;
        '--n-color-checked': string;
        '--n-color-table': string;
        '--n-color-table-modal': string;
        '--n-color-table-popover': string;
        '--n-color-disabled': string;
        '--n-color-disabled-checked': string;
        '--n-text-color': string;
        '--n-text-color-disabled': string;
        '--n-check-mark-color': string;
        '--n-check-mark-color-disabled': string;
        '--n-check-mark-color-disabled-checked': string;
        '--n-font-size': string;
        '--n-label-padding': string;
    }> | undefined;
    themeClass: import("vue").Ref<string, string> | undefined;
    onRender: (() => void) | undefined;
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    size: PropType<"small" | "medium" | "large">;
    checked: {
        type: PropType<boolean | string | number | undefined>;
        default: undefined;
    };
    defaultChecked: {
        type: PropType<boolean | string | number>;
        default: boolean;
    };
    value: PropType<string | number>;
    disabled: {
        type: PropType<boolean | undefined>;
        default: undefined;
    };
    indeterminate: BooleanConstructor;
    label: StringConstructor;
    focusable: {
        type: BooleanConstructor;
        default: boolean;
    };
    checkedValue: {
        type: (BooleanConstructor | StringConstructor | NumberConstructor)[];
        default: boolean;
    };
    uncheckedValue: {
        type: (BooleanConstructor | StringConstructor | NumberConstructor)[];
        default: boolean;
    };
    'onUpdate:checked': PropType<MaybeArray<OnUpdateChecked>>;
    onUpdateChecked: PropType<MaybeArray<OnUpdateChecked>>;
    privateInsideTable: BooleanConstructor;
    onChange: PropType<MaybeArray<OnUpdateChecked>>;
    theme: PropType<import("../../_mixins").Theme<"Checkbox", {
        labelLineHeight: string;
        fontSizeSmall: string;
        fontSizeMedium: string;
        fontSizeLarge: string;
        borderRadius: string;
        color: string;
        colorChecked: string;
        colorDisabled: string;
        colorDisabledChecked: string;
        colorTableHeader: string;
        colorTableHeaderModal: string;
        colorTableHeaderPopover: string;
        checkMarkColor: string;
        checkMarkColorDisabled: string;
        checkMarkColorDisabledChecked: string;
        border: string;
        borderDisabled: string;
        borderDisabledChecked: string;
        borderChecked: string;
        borderFocus: string;
        boxShadowFocus: string;
        textColor: string;
        textColorDisabled: string;
        sizeSmall: string;
        sizeMedium: string;
        sizeLarge: string;
        labelPadding: string;
        labelFontWeight: string;
    }, any>>;
    themeOverrides: PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Checkbox", {
        labelLineHeight: string;
        fontSizeSmall: string;
        fontSizeMedium: string;
        fontSizeLarge: string;
        borderRadius: string;
        color: string;
        colorChecked: string;
        colorDisabled: string;
        colorDisabledChecked: string;
        colorTableHeader: string;
        colorTableHeaderModal: string;
        colorTableHeaderPopover: string;
        checkMarkColor: string;
        checkMarkColorDisabled: string;
        checkMarkColorDisabledChecked: string;
        border: string;
        borderDisabled: string;
        borderDisabledChecked: string;
        borderChecked: string;
        borderFocus: string;
        boxShadowFocus: string;
        textColor: string;
        textColorDisabled: string;
        sizeSmall: string;
        sizeMedium: string;
        sizeLarge: string;
        labelPadding: string;
        labelFontWeight: string;
    }, any>>>;
    builtinThemeOverrides: PropType<import("../../_mixins/use-theme").ExtractThemeOverrides<import("../../_mixins").Theme<"Checkbox", {
        labelLineHeight: string;
        fontSizeSmall: string;
        fontSizeMedium: string;
        fontSizeLarge: string;
        borderRadius: string;
        color: string;
        colorChecked: string;
        colorDisabled: string;
        colorDisabledChecked: string;
        colorTableHeader: string;
        colorTableHeaderModal: string;
        colorTableHeaderPopover: string;
        checkMarkColor: string;
        checkMarkColorDisabled: string;
        checkMarkColorDisabledChecked: string;
        border: string;
        borderDisabled: string;
        borderDisabledChecked: string;
        borderChecked: string;
        borderFocus: string;
        boxShadowFocus: string;
        textColor: string;
        textColorDisabled: string;
        sizeSmall: string;
        sizeMedium: string;
        sizeLarge: string;
        labelPadding: string;
        labelFontWeight: string;
    }, any>>>;
}>> & Readonly<{}>, {
    disabled: boolean | undefined;
    checked: string | number | boolean | undefined;
    indeterminate: boolean;
    focusable: boolean;
    defaultChecked: string | number | boolean;
    checkedValue: string | number | boolean;
    uncheckedValue: string | number | boolean;
    privateInsideTable: boolean;
}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
export default _default;
